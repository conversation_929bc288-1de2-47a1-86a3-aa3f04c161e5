"""
Enhanced logging configuration with colors, emojis, and component identification.

This module provides a custom logging formatter that:
- Uses different colors based on log severity levels
- Adds emojis to identify different components (bot, service, config, db, etc.)
- Provides better visual distinction for different parts of the system
"""

import logging
import sys
from typing import Dict, Optional
from enum import Enum


class LogColor:
    """ANSI color codes for terminal output."""
    
    # Reset
    RESET = '\033[0m'
    
    # Regular colors
    BLACK = '\033[30m'
    RED = '\033[31m'
    GREEN = '\033[32m'
    YELLOW = '\033[33m'
    BLUE = '\033[34m'
    MAGENTA = '\033[35m'
    CYAN = '\033[36m'
    WHITE = '\033[37m'
    
    # Bright colors
    BRIGHT_BLACK = '\033[90m'
    BRIGHT_RED = '\033[91m'
    BRIGHT_GREEN = '\033[92m'
    BRIGHT_YELLOW = '\033[93m'
    BRIGHT_BLUE = '\033[94m'
    BRIGHT_MAGENTA = '\033[95m'
    BRIGHT_CYAN = '\033[96m'
    BRIGHT_WHITE = '\033[97m'
    
    # Background colors
    BG_RED = '\033[41m'
    BG_GREEN = '\033[42m'
    BG_YELLOW = '\033[43m'
    BG_BLUE = '\033[44m'
    
    # Styles
    BOLD = '\033[1m'
    DIM = '\033[2m'
    UNDERLINE = '\033[4m'


class ComponentType(Enum):
    """Component types with their corresponding emojis."""
    
    BOT = "🤖"
    SERVICE = "⚙️"
    CONFIG = "🔧"
    DATABASE = "🗄️"
    CORE = "🏗️"
    PREPROCESS = "🔄"
    LLM = "🧠"
    ACTOR = "🎭"
    SYSTEM = "🖥️"
    UTILS = "🛠️"
    UNKNOWN = "❓"


class ColoredFormatter(logging.Formatter):
    """Custom formatter with colors and emojis for component identification."""
    
    # Color mapping for log levels
    LEVEL_COLORS = {
        logging.DEBUG: LogColor.BRIGHT_BLACK,
        logging.INFO: LogColor.BRIGHT_BLUE,
        logging.WARNING: LogColor.BRIGHT_YELLOW,
        logging.ERROR: LogColor.BRIGHT_RED,
        logging.CRITICAL: LogColor.BG_RED + LogColor.BRIGHT_WHITE,
    }
    
    # Component mapping based on logger name patterns
    COMPONENT_MAPPING = {
        'bot': ComponentType.BOT,
        'service': ComponentType.SERVICE,
        'config': ComponentType.CONFIG,
        'db': ComponentType.DATABASE,
        'database': ComponentType.DATABASE,
        'core': ComponentType.CORE,
        'preprocess': ComponentType.PREPROCESS,
        'llm': ComponentType.LLM,
        'actor': ComponentType.ACTOR,
        'system': ComponentType.SYSTEM,
        'utils': ComponentType.UTILS,
        'main': ComponentType.SYSTEM,
    }
    
    def __init__(self, use_colors: bool = True, show_emojis: bool = True):
        """
        Initialize the colored formatter.
        
        Args:
            use_colors: Whether to use colors in output
            show_emojis: Whether to show component emojis
        """
        super().__init__()
        self.use_colors = use_colors and self._supports_color()
        self.show_emojis = show_emojis
        
        # Base format string
        self.base_format = "%(asctime)s - %(component_emoji)s%(name)s - %(lineno)d- %(levelname)s - %(message)s"
        
    def _supports_color(self) -> bool:
        """Check if the terminal supports color output."""
        return (
            hasattr(sys.stderr, "isatty") and sys.stderr.isatty() and
            hasattr(sys.stdout, "isatty") and sys.stdout.isatty()
        )
    
    def _get_component_type(self, logger_name: str) -> ComponentType:
        """Determine component type from logger name."""
        logger_lower = logger_name.lower()
        
        for pattern, component_type in self.COMPONENT_MAPPING.items():
            if pattern in logger_lower:
                return component_type
        
        return ComponentType.UNKNOWN
    
    def _get_level_color(self, level: int) -> str:
        """Get color for log level."""
        if not self.use_colors:
            return ""
        
        return self.LEVEL_COLORS.get(level, LogColor.WHITE)
    
    def format(self, record: logging.LogRecord) -> str:
        """Format the log record with colors and emojis."""
        # Add component emoji to record
        component_type = self._get_component_type(record.name)
        record.component_emoji = f"{component_type.value} " if self.show_emojis else ""
        
        # Get colors
        level_color = self._get_level_color(record.levelno)
        reset_color = LogColor.RESET if self.use_colors else ""
        
        # Create formatter with colors
        if self.use_colors:
            colored_format = f"{level_color}{self.base_format}{reset_color}"
        else:
            colored_format = self.base_format
        
        formatter = logging.Formatter(colored_format, datefmt='%Y-%m-%d %H:%M:%S')
        
        return formatter.format(record)


class LoggingConfig:
    """Centralized logging configuration manager."""
    
    def __init__(self):
        self.configured = False
        self.handlers: Dict[str, logging.Handler] = {}
    
    def setup_logging(
        self,
        log_level: str = "INFO",
        use_colors: bool = True,
        show_emojis: bool = True,
        log_to_file: bool = False,
        log_file_path: Optional[str] = None
    ) -> None:
        """
        Set up enhanced logging configuration.
        
        Args:
            log_level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
            use_colors: Whether to use colors in console output
            show_emojis: Whether to show component emojis
            log_to_file: Whether to log to a file
            log_file_path: Path to log file (if log_to_file is True)
        """
        if self.configured:
            return
        
        # Convert log level string to logging constant
        numeric_level = getattr(logging, log_level.upper(), logging.INFO)
        
        # Clear any existing handlers
        root_logger = logging.getLogger()
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)
        
        # Create console handler with colored formatter
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(numeric_level)
        console_formatter = ColoredFormatter(use_colors=use_colors, show_emojis=show_emojis)
        console_handler.setFormatter(console_formatter)
        
        # Add console handler
        root_logger.addHandler(console_handler)
        self.handlers['console'] = console_handler
        
        # Create file handler if requested
        if log_to_file:
            file_path = log_file_path or "zeitwahl.log"
            file_handler = logging.FileHandler(file_path)
            file_handler.setLevel(numeric_level)
            # File logs don't need colors but can have emojis
            file_formatter = ColoredFormatter(use_colors=False, show_emojis=show_emojis)
            file_handler.setFormatter(file_formatter)
            
            root_logger.addHandler(file_handler)
            self.handlers['file'] = file_handler
        
        # Set root logger level
        root_logger.setLevel(numeric_level)
        
        # Configure specific loggers for noisy libraries
        self._configure_library_loggers()
        
        self.configured = True
        
        # Log configuration success
        logger = logging.getLogger(__name__)
        logger.info(f"🎨 Enhanced logging configured at {log_level} level")
        logger.info(f"Colors: {'✅' if use_colors else '❌'}, Emojis: {'✅' if show_emojis else '❌'}")
        if log_to_file:
            logger.info(f"File logging: {log_file_path or 'zeitwahl.log'}")
    
    def _configure_library_loggers(self) -> None:
        """Configure log levels for noisy third-party libraries."""
        library_configs = {
            'aiogram': logging.WARNING,
            'httpx': logging.WARNING,
            'urllib3': logging.WARNING,
            'pymongo': logging.WARNING,
            'asyncio': logging.WARNING,
        }
        
        for library, level in library_configs.items():
            logging.getLogger(library).setLevel(level)
    
    def add_custom_handler(self, name: str, handler: logging.Handler) -> None:
        """Add a custom logging handler."""
        if name in self.handlers:
            logging.getLogger().removeHandler(self.handlers[name])
        
        logging.getLogger().addHandler(handler)
        self.handlers[name] = handler
    
    def remove_handler(self, name: str) -> None:
        """Remove a logging handler."""
        if name in self.handlers:
            logging.getLogger().removeHandler(self.handlers[name])
            del self.handlers[name]
    
    def get_logger(self, name: str) -> logging.Logger:
        """Get a logger with the configured formatting."""
        return logging.getLogger(name)


# Global logging configuration instance
logging_config = LoggingConfig()


def setup_enhanced_logging(
    log_level: str = "INFO",
    use_colors: bool = True,
    show_emojis: bool = True,
    log_to_file: bool = False,
    log_file_path: Optional[str] = None
) -> None:
    """
    Convenience function to set up enhanced logging.
    
    Args:
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        use_colors: Whether to use colors in console output
        show_emojis: Whether to show component emojis
        log_to_file: Whether to log to a file
        log_file_path: Path to log file (if log_to_file is True)
    """
    logging_config.setup_logging(
        log_level=log_level,
        use_colors=use_colors,
        show_emojis=show_emojis,
        log_to_file=log_to_file,
        log_file_path=log_file_path
    )


def get_logger(name: str) -> logging.Logger:
    """
    Get a logger with enhanced formatting.
    
    Args:
        name: Logger name (usually __name__)
    
    Returns:
        Configured logger instance
    """
    return logging_config.get_logger(name)
