"""
Database Actor for handling all database operations through the event bus.

This actor wraps the DatabaseService and provides database operations
through event-driven communication, handling user management and message storage.
"""

import logging
from typing import Dict, Any, Optional

from app.core.actor import Actor
from app.core.event_bus import event_bus
from app.core.message_context import message_context_manager
from app.db import database_service
from app.common.models import BotUser, ConversationMessage
from app.utils.events import (
    UserMessageReceived, UserIdentified, MessageSent,
    ErrorOccurred
)

logger = logging.getLogger(__name__)


class DBActor(Actor):
    """Actor responsible for all database operations."""
    
    def __init__(self, name: Optional[str] = None):
        super().__init__(name or "DBActor")
        self.db_service = database_service
    
    async def initialize(self) -> None:
        """Initialize the database actor."""
        try:
            logger.info(f"Initializing {self.name}")
            
            # Initialize database service
            await self.db_service.initialize()
            
            logger.info(f"{self.name} initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize {self.name}: {e}")
            raise
    
    async def start_actor(self) -> None:
        """Start the database actor."""
        logger.info(f"{self.name} started and ready to handle database operations")
    
    async def stop_actor(self) -> None:
        """Stop the database actor."""
        try:
            # Close database connections if needed
            await self.db_service.close()
            logger.info(f"{self.name} stopped successfully")
        except Exception as e:
            logger.error(f"Error stopping {self.name}: {e}")
    
    @event_bus.subscribe("user_identification_request")
    async def handle_user_identification(self, event_data: Dict[str, Any]) -> None:
        """Handle user identification requests."""
        try:
            telegram_user_id = event_data.get("telegram_user_id")
            chat_id = event_data.get("chat_id")
            telegram_message_id = event_data.get("telegram_message_id")
            user_info = event_data.get("user_info", {})

            if not telegram_user_id or not chat_id or not telegram_message_id:
                logger.error("Missing required fields in user identification request")
                return

            logger.debug(f"Identifying user {telegram_user_id}")

            # Check if user exists
            existing_user = await self.db_service.get_user_by_telegram_id(telegram_user_id)
            
            if existing_user:
                # User exists, update last seen
                existing_user.last_seen_at = event_data.get("timestamp")
                await self.db_service.update_user(existing_user)
                
                # Publish user identified event
                await event_bus.publish("user_identified", UserIdentified(
                    telegram_user_id=telegram_user_id,
                    chat_id=chat_id,
                    telegram_message_id=telegram_message_id,
                    user_exists=True,
                    user_data={
                        "telegram_user_id": existing_user.telegram_user_id,
                        "username": existing_user.username,
                        "first_name": existing_user.first_name,
                        "last_name": existing_user.last_name,
                        "timezone": existing_user.timezone,
                        "is_active": existing_user.is_active,
                        "total_messages": existing_user.total_messages,
                        "created_at": existing_user.created_at,
                        "last_seen_at": existing_user.last_seen_at
                    },
                    needs_registration=False
                ))
            else:
                # New user, publish event indicating registration needed
                await event_bus.publish("user_identified", UserIdentified(
                    telegram_user_id=telegram_user_id,
                    chat_id=chat_id,
                    telegram_message_id=telegram_message_id,
                    user_exists=False,
                    user_data=None,
                    needs_registration=True
                ))
            
        except Exception as e:
            logger.error(f"Error in user identification: {e}")
            await self._publish_error("user_identification", str(e), event_data)
    
    @event_bus.subscribe("user_registration_request")
    async def handle_user_registration(self, event_data: Dict[str, Any]) -> None:
        """Handle new user registration."""
        try:
            telegram_user_id = event_data.get("telegram_user_id")
            user_info = event_data.get("user_info", {})
            
            logger.info(f"Registering new user {telegram_user_id}")
            
            # Create new user
            new_user = BotUser(
                telegram_user_id=telegram_user_id or 0,
                username=user_info.get("username"),
                first_name=user_info.get("first_name"),
                last_name=user_info.get("last_name"),
                language_code=user_info.get("language_code"),
                timezone=user_info.get("timezone", "UTC"),
                is_bot=user_info.get("is_bot", False)
            )
            
            # Save to database
            created_user = await self.db_service.create_user(new_user)
            
            # Publish user registered event
            await event_bus.publish("user_registered", {
                "telegram_user_id": telegram_user_id,
                "user_data": {
                    "telegram_user_id": created_user.telegram_user_id,
                    "username": created_user.username,
                    "first_name": created_user.first_name,
                    "last_name": created_user.last_name,
                    "timezone": created_user.timezone,
                    "is_active": created_user.is_active,
                    "created_at": created_user.created_at
                },
                "success": True
            })
            
        except Exception as e:
            logger.error(f"Error in user registration: {e}")
            await self._publish_error("user_registration", str(e), event_data)
    
    @event_bus.subscribe("store_message_request")
    async def handle_store_message(self, event_data: Dict[str, Any]) -> None:
        """Handle message storage requests."""
        try:
            # Validate required fields
            telegram_message_id = event_data.get("telegram_message_id")
            chat_id = event_data.get("chat_id")
            message_text = event_data.get("message_text")
            telegram_timestamp = event_data.get("telegram_timestamp")

            if not all([telegram_message_id, chat_id, message_text, telegram_timestamp]):
                logger.error("Missing required fields in store message request")
                return

            # Type assertions after validation
            assert telegram_message_id is not None
            assert chat_id is not None
            assert message_text is not None
            assert telegram_timestamp is not None

            # Create ConversationMessage from event data
            message = ConversationMessage(
                telegram_message_id=int(telegram_message_id),
                chat_id=int(chat_id),
                telegram_user_id=event_data.get("telegram_user_id"),
                message_text=str(message_text),
                message_type=event_data.get("message_type", "text"),
                is_from_bot=event_data.get("is_from_bot", False),
                telegram_timestamp=telegram_timestamp,
                is_reply=event_data.get("is_reply", False),
                reply_to_message_id=event_data.get("reply_to_message_id"),
                chat_type=event_data.get("chat_type", "private"),
                chat_title=event_data.get("chat_title"),
                processing_status="received"
            )
            
            # Store message in database
            stored_message = await self.db_service.create_message(message)
            
            # Publish message stored event
            await event_bus.publish("message_stored", {
                "message_id": str(stored_message.id),
                "telegram_message_id": stored_message.telegram_message_id,
                "chat_id": stored_message.chat_id,
                "success": True
            })
            
        except Exception as e:
            logger.error(f"Error storing message: {e}")
            await self._publish_error("store_message", str(e), event_data)
    
    @event_bus.subscribe("get_conversation_history_request")
    async def handle_get_conversation_history(self, event_data: Dict[str, Any]) -> None:
        """Handle conversation history requests."""
        try:
            chat_id = event_data.get("chat_id")
            telegram_user_id = event_data.get("telegram_user_id")
            limit = event_data.get("limit", 10)

            if not chat_id or not telegram_user_id:
                logger.error("Missing required fields in conversation history request")
                return

            # Get conversation history
            messages = await self.db_service.get_conversation_history(
                int(chat_id), int(telegram_user_id), limit=limit
            )
            
            # Convert to serializable format
            history = []
            for message in messages:
                history.append({
                    "message_text": message.message_text,
                    "is_from_bot": message.is_from_bot,
                    "timestamp": message.telegram_timestamp,
                    "message_type": message.message_type,
                    "processing_status": message.processing_status
                })
            
            # Publish conversation history
            await event_bus.publish("conversation_history_response", {
                "chat_id": chat_id,
                "telegram_user_id": telegram_user_id,
                "history": history,
                "success": True
            })
            
        except Exception as e:
            logger.error(f"Error getting conversation history: {e}")
            await self._publish_error("get_conversation_history", str(e), event_data)
    
    @event_bus.subscribe("update_message_status_request")
    async def handle_update_message_status(self, event_data: Dict[str, Any]) -> None:
        """Handle message status update requests."""
        try:
            telegram_message_id = event_data.get("telegram_message_id")
            chat_id = event_data.get("chat_id")
            new_status = event_data.get("status")

            if not telegram_message_id or not chat_id or not new_status:
                logger.error("Missing required fields in update message status request")
                return

            # Get the message
            message = await self.db_service.get_message_by_telegram_id(
                int(telegram_message_id), int(chat_id)
            )

            if message:
                # Update status
                message.processing_status = str(new_status)
                await self.db_service.update_message(message)
                
                # Publish status updated event
                await event_bus.publish("message_status_updated", {
                    "telegram_message_id": telegram_message_id,
                    "chat_id": chat_id,
                    "status": new_status,
                    "success": True
                })
            else:
                logger.warning(f"Message not found: {telegram_message_id} in chat {chat_id}")
                
        except Exception as e:
            logger.error(f"Error updating message status: {e}")
            await self._publish_error("update_message_status", str(e), event_data)
    
    async def _publish_error(self, operation: str, error_message: str, original_event: Dict[str, Any]) -> None:
        """Publish an error event."""
        await event_bus.publish("error_occurred", ErrorOccurred(
            user_id=original_event.get("telegram_user_id"),
            chat_id=original_event.get("chat_id"),
            message_id=original_event.get("telegram_message_id"),
            error_type="database_error",
            error_message=error_message,
            component=f"{self.name}.{operation}"
        ))
