"""
Database package for MongoDB operations with event bus integration.

This package provides database connection management, repositories,
and event-driven database operations for MongoDB Atlas integration.
"""

from app.infrastructure.database import (
    ConnectionManager, get_connection_manager,
    IDatabaseService, IUserRepository, IMessageRepository, IDatabaseConnection,
    BotUserRepository, ConversationMessageRepository,
    DatabaseService, database_service,
    DBActor
)

__all__ = [
    "ConnectionManager",
    "get_connection_manager",
    "IDatabaseService",
    "IUserRepository",
    "IMessageRepository",
    "IDatabaseConnection",
    "BotUserRepository",
    "ConversationMessageRepository",
    "DatabaseService",
    "database_service",
    "DBActor"
]
