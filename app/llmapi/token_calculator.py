"""
Token Calculator Service for managing LLM token usage and context length.

This service calculates token usage, manages context length limits,
and optimizes prompts to fit within model constraints.
"""

import logging
import re
from typing import Dict, Any, Optional, List, Tuple

logger = logging.getLogger(__name__)


class TokenCalculator:
    """Service for calculating and managing LLM tokens."""
    
    def __init__(self):
        # Model-specific token limits (approximate)
        self.model_limits = {
            "gpt-3.5-turbo": 4096,
            "gpt-4": 8192,
            "gpt-4-32k": 32768,
            "gemini-pro": 30720,
            "deepseek-chat": 32768,
            "claude-3-sonnet": 200000,
            "claude-3-haiku": 200000,
            "default": 4096
        }
        
        # Token cost estimates (tokens per dollar, approximate)
        self.token_costs = {
            "gpt-3.5-turbo": {"input": 0.0015, "output": 0.002},  # per 1K tokens
            "gpt-4": {"input": 0.03, "output": 0.06},
            "gemini-pro": {"input": 0.00025, "output": 0.0005},
            "deepseek-chat": {"input": 0.00014, "output": 0.00028},
            "default": {"input": 0.001, "output": 0.002}
        }
    
    def estimate_tokens(self, text: str) -> int:
        """
        Estimate token count for a given text.
        
        This is a rough estimation. For production use, integrate with
        the actual tokenizer for the specific model being used.
        
        Args:
            text: Text to estimate tokens for
            
        Returns:
            Estimated token count
        """
        if not text:
            return 0
        
        # Basic estimation: ~4 characters per token for English text
        # This varies by model and language, but gives a reasonable approximation
        char_count = len(text)
        
        # Adjust for different types of content
        # Code and special characters tend to use more tokens
        if self._contains_code(text):
            return int(char_count / 3)  # Code uses more tokens
        elif self._contains_special_chars(text):
            return int(char_count / 3.5)  # Special chars use more tokens
        else:
            return int(char_count / 4)  # Regular text
    
    def get_model_limit(self, model_name: str) -> int:
        """Get the token limit for a specific model."""
        return self.model_limits.get(model_name, self.model_limits["default"])
    
    def calculate_remaining_tokens(
        self, 
        prompt: str, 
        model_name: str,
        reserve_for_response: int = 1000
    ) -> int:
        """
        Calculate how many tokens are available for the response.
        
        Args:
            prompt: The input prompt
            model_name: Name of the model being used
            reserve_for_response: Tokens to reserve for the response
            
        Returns:
            Number of tokens available for response
        """
        prompt_tokens = self.estimate_tokens(prompt)
        model_limit = self.get_model_limit(model_name)
        
        available_tokens = model_limit - prompt_tokens - reserve_for_response
        return max(0, available_tokens)
    
    def optimize_prompt_length(
        self, 
        prompt: str, 
        model_name: str,
        max_response_tokens: int = 1000
    ) -> Tuple[str, Dict[str, Any]]:
        """
        Optimize prompt length to fit within model limits.
        
        Args:
            prompt: Original prompt
            model_name: Model being used
            max_response_tokens: Maximum tokens needed for response
            
        Returns:
            Tuple of (optimized_prompt, optimization_info)
        """
        model_limit = self.get_model_limit(model_name)
        prompt_tokens = self.estimate_tokens(prompt)
        
        optimization_info = {
            "original_tokens": prompt_tokens,
            "model_limit": model_limit,
            "target_tokens": model_limit - max_response_tokens,
            "was_truncated": False,
            "truncation_method": None
        }
        
        target_tokens = model_limit - max_response_tokens
        
        if prompt_tokens <= target_tokens:
            # Prompt fits within limits
            optimization_info["optimized_tokens"] = prompt_tokens
            return prompt, optimization_info
        
        # Need to truncate the prompt
        optimization_info["was_truncated"] = True
        
        # Try different truncation strategies
        optimized_prompt = self._truncate_prompt_intelligently(prompt, target_tokens)
        optimization_info["optimized_tokens"] = self.estimate_tokens(optimized_prompt)
        optimization_info["truncation_method"] = "intelligent"
        
        return optimized_prompt, optimization_info
    
    def _truncate_prompt_intelligently(self, prompt: str, target_tokens: int) -> str:
        """
        Intelligently truncate a prompt to fit within token limits.
        
        Priority order for truncation:
        1. Remove oldest conversation history
        2. Reduce context details
        3. Truncate user context
        4. Keep system context and current message
        """
        sections = self._split_prompt_into_sections(prompt)
        
        # Start with all sections
        current_prompt = prompt
        current_tokens = self.estimate_tokens(current_prompt)
        
        # Remove conversation history first (oldest messages)
        if current_tokens > target_tokens and "conversation_history" in sections:
            history_lines = sections["conversation_history"].split('\n')
            # Keep system message and recent messages, remove older ones
            if len(history_lines) > 5:
                reduced_history = '\n'.join(history_lines[:2] + history_lines[-3:])
                current_prompt = current_prompt.replace(sections["conversation_history"], reduced_history)
                current_tokens = self.estimate_tokens(current_prompt)
        
        # Reduce context details if still too long
        if current_tokens > target_tokens and "user_context" in sections:
            # Simplify user context
            simplified_context = self._simplify_user_context(sections["user_context"])
            current_prompt = current_prompt.replace(sections["user_context"], simplified_context)
            current_tokens = self.estimate_tokens(current_prompt)
        
        # Last resort: truncate from the end but keep current message
        if current_tokens > target_tokens:
            target_chars = int(target_tokens * 4)  # Rough conversion back to chars
            if len(current_prompt) > target_chars:
                # Find the current message section and preserve it
                current_msg_start = current_prompt.rfind("Current Request:")
                if current_msg_start > 0:
                    preserved_end = current_prompt[current_msg_start:]
                    available_chars = target_chars - len(preserved_end)
                    if available_chars > 0:
                        current_prompt = current_prompt[:available_chars] + "\n\n" + preserved_end
                else:
                    current_prompt = current_prompt[:target_chars]
        
        return current_prompt
    
    def _split_prompt_into_sections(self, prompt: str) -> Dict[str, str]:
        """Split prompt into identifiable sections for intelligent truncation."""
        sections = {}
        
        # Look for common section headers
        section_patterns = {
            "system_context": r"You are Zeitwahl.*?(?=\n\n|\nUser Context:|\nRecent Conversation|\nCurrent Request:)",
            "user_context": r"User Context:.*?(?=\n\n|\nRecent Conversation|\nCurrent Request:)",
            "conversation_history": r"Recent Conversation History:.*?(?=\n\n|\nCurrent Request:)",
            "current_request": r"Current Request:.*"
        }
        
        for section_name, pattern in section_patterns.items():
            match = re.search(pattern, prompt, re.DOTALL | re.IGNORECASE)
            if match:
                sections[section_name] = match.group()
        
        return sections
    
    def _simplify_user_context(self, user_context: str) -> str:
        """Simplify user context to reduce token usage."""
        lines = user_context.split('\n')
        essential_lines = []
        
        for line in lines:
            # Keep essential information
            if any(keyword in line.lower() for keyword in ["name:", "timezone:", "user context:"]):
                essential_lines.append(line)
        
        return '\n'.join(essential_lines) if essential_lines else "User Context: Available"
    
    def _contains_code(self, text: str) -> bool:
        """Check if text contains code-like content."""
        code_indicators = [
            r'```',  # Code blocks
            r'def\s+\w+\(',  # Python functions
            r'function\s+\w+\(',  # JavaScript functions
            r'class\s+\w+',  # Class definitions
            r'import\s+\w+',  # Import statements
            r'{\s*\w+:\s*\w+',  # JSON-like objects
        ]
        
        return any(re.search(pattern, text) for pattern in code_indicators)
    
    def _contains_special_chars(self, text: str) -> bool:
        """Check if text contains many special characters."""
        special_char_count = len(re.findall(r'[^\w\s]', text))
        return special_char_count / len(text) > 0.1 if text else False
    
    def estimate_cost(
        self, 
        input_tokens: int, 
        output_tokens: int, 
        model_name: str
    ) -> Dict[str, float]:
        """
        Estimate the cost of a request.
        
        Args:
            input_tokens: Number of input tokens
            output_tokens: Number of output tokens
            model_name: Model being used
            
        Returns:
            Dictionary with cost breakdown
        """
        costs = self.token_costs.get(model_name, self.token_costs["default"])
        
        input_cost = (input_tokens / 1000) * costs["input"]
        output_cost = (output_tokens / 1000) * costs["output"]
        total_cost = input_cost + output_cost
        
        return {
            "input_cost": input_cost,
            "output_cost": output_cost,
            "total_cost": total_cost,
            "input_tokens": input_tokens,
            "output_tokens": output_tokens,
            "model": model_name
        }
    
    def get_token_usage_stats(self) -> Dict[str, Any]:
        """Get statistics about token usage (placeholder for future implementation)."""
        return {
            "total_requests": 0,
            "total_input_tokens": 0,
            "total_output_tokens": 0,
            "total_cost": 0.0,
            "average_tokens_per_request": 0,
            "most_used_model": "unknown"
        }
