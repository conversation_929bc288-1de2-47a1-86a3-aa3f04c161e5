"""
Conversation Message Repository for managing message data in MongoDB.

This repository handles all database operations related to conversation messages,
including creation, retrieval, updates, and message management.
"""

import logging
from typing import Optional, List
from datetime import datetime, timezone, timedelta

from app.common.models import ConversationMessage
from ..connection_manager import get_connection_manager

logger = logging.getLogger(__name__)


class ConversationMessageRepository:
    """Repository for conversation message operations."""
    
    def __init__(self):
        self.collection_name = "conversation_messages"
        self.connection_manager = get_connection_manager()
    
    async def create_message(self, message: ConversationMessage) -> ConversationMessage:
        """Create a new message in the database."""
        try:
            collection = self.connection_manager.get_collection(self.collection_name)
            
            # Convert message to dict for MongoDB
            message_dict = message.to_dict()
            
            # Insert the message
            result = collection.insert_one(message_dict)
            
            # Update the message with the generated ID
            message.id = result.inserted_id
            
            logger.debug(f"Created message {message.telegram_message_id}")
            return message
            
        except Exception as e:
            logger.error(f"Error creating message: {e}")
            raise
    
    async def get_message_by_telegram_id(
        self, 
        telegram_message_id: int, 
        chat_id: int
    ) -> Optional[ConversationMessage]:
        """Get a message by its Telegram message ID and chat ID."""
        try:
            collection = self.connection_manager.get_collection(self.collection_name)
            
            # Find the message
            message_doc = collection.find_one({
                "telegram_message_id": telegram_message_id,
                "chat_id": chat_id
            })
            
            if message_doc:
                return ConversationMessage.from_dict(message_doc)
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting message by telegram ID {telegram_message_id}: {e}")
            raise
    
    async def update_message(self, message: ConversationMessage) -> ConversationMessage:
        """Update an existing message."""
        try:
            collection = self.connection_manager.get_collection(self.collection_name)
            
            # Update the message
            message_dict = message.to_dict()
            message_dict["updated_at"] = datetime.now(timezone.utc)
            
            result = collection.update_one(
                {
                    "telegram_message_id": message.telegram_message_id,
                    "chat_id": message.chat_id
                },
                {"$set": message_dict}
            )
            
            if result.matched_count == 0:
                raise ValueError(f"Message {message.telegram_message_id} not found")
            
            logger.debug(f"Updated message {message.telegram_message_id}")
            return message
            
        except Exception as e:
            logger.error(f"Error updating message {message.telegram_message_id}: {e}")
            raise
    
    async def get_conversation_history(
        self,
        chat_id: int,
        telegram_user_id: int,
        limit: int = 10,
        offset: int = 0
    ) -> List[ConversationMessage]:
        """Get conversation history for a user in a chat."""
        try:
            collection = self.connection_manager.get_collection(self.collection_name)
            
            # Get messages from the chat, ordered by timestamp (newest first)
            cursor = collection.find({
                "chat_id": chat_id,
                "$or": [
                    {"telegram_user_id": telegram_user_id},
                    {"is_from_bot": True}
                ]
            }).sort("telegram_timestamp", -1).skip(offset).limit(limit)
            
            messages = []
            for message_doc in cursor:
                messages.append(ConversationMessage.from_dict(message_doc))
            
            return messages
            
        except Exception as e:
            logger.error(f"Error getting conversation history: {e}")
            raise
    
    async def get_recent_messages(
        self,
        chat_id: int,
        hours: int = 24,
        limit: int = 100
    ) -> List[ConversationMessage]:
        """Get recent messages in a chat."""
        try:
            collection = self.connection_manager.get_collection(self.collection_name)
            
            cutoff_time = datetime.now(timezone.utc) - timedelta(hours=hours)
            
            cursor = collection.find({
                "chat_id": chat_id,
                "telegram_timestamp": {"$gte": cutoff_time}
            }).sort("telegram_timestamp", -1).limit(limit)
            
            messages = []
            for message_doc in cursor:
                messages.append(ConversationMessage.from_dict(message_doc))
            
            return messages
            
        except Exception as e:
            logger.error(f"Error getting recent messages: {e}")
            raise
    
    async def get_unprocessed_messages(self, limit: int = 100) -> List[ConversationMessage]:
        """Get unprocessed messages."""
        try:
            collection = self.connection_manager.get_collection(self.collection_name)
            
            cursor = collection.find({
                "processing_status": {"$in": ["received", "processing"]}
            }).sort("telegram_timestamp", 1).limit(limit)
            
            messages = []
            for message_doc in cursor:
                messages.append(ConversationMessage.from_dict(message_doc))
            
            return messages
            
        except Exception as e:
            logger.error(f"Error getting unprocessed messages: {e}")
            raise
    
    async def mark_message_as_processed(
        self,
        telegram_message_id: int,
        chat_id: int,
        response_text: str,
        processing_time: float
    ) -> bool:
        """Mark a message as processed."""
        try:
            collection = self.connection_manager.get_collection(self.collection_name)
            
            result = collection.update_one(
                {
                    "telegram_message_id": telegram_message_id,
                    "chat_id": chat_id
                },
                {
                    "$set": {
                        "processing_status": "processed",
                        "response_text": response_text,
                        "processing_time": processing_time,
                        "processed_at": datetime.now(timezone.utc)
                    }
                }
            )
            
            return result.matched_count > 0
            
        except Exception as e:
            logger.error(f"Error marking message as processed: {e}")
            raise
    
    async def delete_old_messages(self, days: int = 30) -> int:
        """Delete old messages."""
        try:
            collection = self.connection_manager.get_collection(self.collection_name)
            
            cutoff_date = datetime.now(timezone.utc) - timedelta(days=days)
            
            result = collection.delete_many({
                "telegram_timestamp": {"$lt": cutoff_date}
            })
            
            logger.info(f"Deleted {result.deleted_count} old messages")
            return result.deleted_count
            
        except Exception as e:
            logger.error(f"Error deleting old messages: {e}")
            raise
    
    async def get_message_count(self, chat_id: Optional[int] = None) -> int:
        """Get total number of messages."""
        try:
            collection = self.connection_manager.get_collection(self.collection_name)
            
            query = {}
            if chat_id:
                query["chat_id"] = chat_id
            
            return collection.count_documents(query)
            
        except Exception as e:
            logger.error(f"Error getting message count: {e}")
            raise
