"""
Bot Actor for handling Telegram bot operations through the event bus.

This actor wraps the TelegramBot functionality, handles message polling,
and sends messages through event-driven communication.
"""

import logging
from datetime import datetime
from typing import Dict, Any, Optional

from aiogram import Bo<PERSON>, Dispatcher
from aiogram.filters import Command
from aiogram.types import Message

from app.core.actor import Actor
from app.core.event_bus import event_bus
from app.core.message_context import message_context_manager
from app.config import settings
from app.config.prompts import SYSTEM_PROMPTS
from app.utils.events import (
    UserMessageReceived, SendMessageRequest, MessageSent,
    GenericResponse, ErrorOccurred
)

logger = logging.getLogger(__name__)


class BotActor(Actor):
    """Actor responsible for Telegram bot operations."""
    
    def __init__(self, name: Optional[str] = None):
        super().__init__(name or "BotActor")
        self.bot = None
        self.dp = None
        self.user_sessions: Dict[int, datetime] = {}
    
    async def initialize(self) -> None:
        """Initialize the bot actor."""
        try:
            logger.info(f"Initializing {self.name}")
            
            # Initialize bot and dispatcher
            self.bot = Bot(token=settings.telegram.bot_token)
            self.dp = Dispatcher()
            
            # Register handlers
            self._register_handlers()
            
            logger.info(f"{self.name} initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize {self.name}: {e}")
            raise
    
    async def start_actor(self) -> None:
        """Start the bot actor."""
        logger.info(f"Starting {self.name} polling...")
        
        # Start polling in the background
        await self.dp.start_polling(self.bot)
    
    async def stop_actor(self) -> None:
        """Stop the bot actor."""
        try:
            logger.info(f"Stopping {self.name}...")
            
            # Stop polling
            await self.dp.stop_polling()
            
            # Close bot session
            if self.bot:
                await self.bot.session.close()
            
            logger.info(f"{self.name} stopped successfully")
            
        except Exception as e:
            logger.error(f"Error stopping {self.name}: {e}")
    
    def _register_handlers(self):
        """Register message and command handlers."""
        self.dp.message.register(self.handle_start_command, Command("start"))
        self.dp.message.register(self.handle_help_command, Command("help"))
        self.dp.message.register(self.handle_message)
    
    async def handle_start_command(self, message: Message):
        """Handle /start command."""
        try:
            if not message.from_user:
                logger.warning("Cannot handle start command: missing user information")
                return
            
            user_id = message.from_user.id
            
            # Track user session
            await self._start_user_session(message)
            
            # Send welcome message
            await message.reply(
                SYSTEM_PROMPTS["welcome_message"],
                parse_mode="Markdown"
            )
            
            logger.info(f"User {user_id} started session")
            
        except Exception as e:
            logger.error(f"Error in start command: {e}")
            await self._handle_error(message, "start_command", str(e))
    
    async def handle_help_command(self, message: Message):
        """Handle /help command."""
        try:
            await message.reply(
                SYSTEM_PROMPTS["help_message"],
                parse_mode="Markdown"
            )
            
        except Exception as e:
            logger.error(f"Error in help command: {e}")
            await self._handle_error(message, "help_command", str(e))
    
    async def handle_message(self, message: Message):
        """Handle incoming messages."""
        try:
            if not message.from_user:
                logger.warning("Cannot handle message: missing user information")
                return
            
            # Update user session
            self.user_sessions[message.from_user.id] = datetime.now()
            
            # Create and publish user message received event
            user_message_event = UserMessageReceived(
                telegram_user_id=message.from_user.id,
                chat_id=message.chat.id,
                telegram_message_id=message.message_id,
                message_text=message.text or "",
                message_type=self._get_message_type(message),
                username=message.from_user.username,
                first_name=message.from_user.first_name,
                last_name=message.from_user.last_name,
                telegram_timestamp=message.date,
                chat_type=message.chat.type,
                chat_title=message.chat.title,
                is_reply=message.reply_to_message is not None,
                reply_to_message_id=message.reply_to_message.message_id if message.reply_to_message else None
            )
            
            # Publish the event for preprocessing
            await event_bus.publish("user_message_received", user_message_event)
            
            logger.debug(f"Published message from user {message.from_user.id}")
            
        except Exception as e:
            logger.error(f"Error handling message: {e}")
            await self._handle_error(message, "handle_message", str(e))
    
    @event_bus.subscribe("send_message_request")
    async def handle_send_message_request(self, event_data: SendMessageRequest) -> None:
        """Handle requests to send messages."""
        try:
            # Send the message
            sent_message = await self.bot.send_message(
                chat_id=event_data.chat_id,
                text=event_data.message_text,
                reply_to_message_id=event_data.reply_to_message_id,
                parse_mode=event_data.parse_mode
            )
            
            # Publish message sent event
            await event_bus.publish("message_sent", MessageSent(
                chat_id=event_data.chat_id,
                telegram_message_id=sent_message.message_id,
                message_text=event_data.message_text,
                success=True
            ))
            
            # Store the bot message in database
            await event_bus.publish("store_message_request", {
                "telegram_message_id": sent_message.message_id,
                "chat_id": event_data.chat_id,
                "telegram_user_id": None,  # Bot message
                "message_text": event_data.message_text,
                "message_type": "text",
                "is_from_bot": True,
                "telegram_timestamp": sent_message.date,
                "is_reply": event_data.reply_to_message_id is not None,
                "reply_to_message_id": event_data.reply_to_message_id,
                "chat_type": "private",  # We'll need to get this from context
                "chat_title": None
            })
            
            logger.debug(f"Sent message to chat {event_data.chat_id}")
            
        except Exception as e:
            logger.error(f"Error sending message: {e}")
            
            # Publish failed message event
            await event_bus.publish("message_sent", MessageSent(
                chat_id=event_data.chat_id,
                telegram_message_id=0,
                message_text=event_data.message_text,
                success=False,
                error_message=str(e)
            ))
    
    @event_bus.subscribe("generic_response")
    async def handle_generic_response(self, event_data: GenericResponse) -> None:
        """Handle generic response requests."""
        try:
            response_text = self._get_generic_response_text(event_data.message_type)
            
            # Send the response
            await event_bus.publish("send_message_request", SendMessageRequest(
                chat_id=event_data.chat_id,
                message_text=response_text,
                reply_to_message_id=event_data.reply_to_message_id
            ))
            
        except Exception as e:
            logger.error(f"Error handling generic response: {e}")
    
    def _get_generic_response_text(self, message_type: str) -> str:
        """Get the appropriate generic response text."""
        responses = {
            "new_user": """Hello! Welcome to Zeitwahl, your AI scheduling assistant.

I notice you're new here. To provide you with the best assistance, I'd like to know your timezone.

Please tell me:
1. What timezone are you in? (e.g., "UTC", "America/New_York", "Europe/London")
2. How would you like me to help you with scheduling and time management?

You can also type /help to see what I can do for you.""",
            
            "irrelevant_message": """I'm Zeitwahl, your AI scheduling and time management assistant. 

I specialize in helping with:
• Calendar management and scheduling
• Time zone conversions  
• Meeting planning and coordination
• Productivity and time management advice
• Date and time calculations

Your message doesn't seem to be related to scheduling or time management. How can I help you with your calendar or time-related needs today?""",
            
            "error": "I apologize, but I encountered an error processing your message. Please try again or contact support if the issue persists."
        }
        
        return responses.get(message_type, responses["error"])
    
    def _get_message_type(self, message: Message) -> str:
        """Determine the type of message."""
        if message.text:
            return "text"
        elif message.photo:
            return "photo"
        elif message.document:
            return "document"
        elif message.voice:
            return "voice"
        elif message.video:
            return "video"
        elif message.sticker:
            return "sticker"
        else:
            return "other"
    
    async def _start_user_session(self, message: Message):
        """Start a user session."""
        user_id = message.from_user.id
        self.user_sessions[user_id] = datetime.now()
        
        # Publish user session started event
        await event_bus.publish("user_session_started", {
            "user_id": user_id,
            "chat_id": message.chat.id,
            "username": message.from_user.username,
            "first_name": message.from_user.first_name,
            "last_name": message.from_user.last_name,
            "timestamp": datetime.now()
        })
    
    async def _handle_error(self, message: Message, operation: str, error_message: str):
        """Handle errors and publish error events."""
        try:
            # Publish error event
            await event_bus.publish("error_occurred", ErrorOccurred(
                user_id=message.from_user.id if message.from_user else None,
                chat_id=message.chat.id,
                message_id=message.message_id,
                error_type="bot_error",
                error_message=error_message,
                component=f"{self.name}.{operation}"
            ))
            
            # Send user-friendly error message
            await message.reply(SYSTEM_PROMPTS["error_messages"]["general_error"])
            
        except Exception as e:
            logger.error(f"Failed to handle error: {e}")
            # If we can't send error message, just log it
