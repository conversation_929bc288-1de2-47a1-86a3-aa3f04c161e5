"""
Preprocessing Actor for handling message preprocessing and context building.

This actor receives messages from <PERSON>t<PERSON><PERSON>, identifies users, validates messages,
grabs context, and builds comprehensive prompts for LLM processing.
"""

import logging
from typing import Dict, Any, Optional

from app.core.actors.actor import Actor
from app.core.events.event_bus import event_bus
from app.core.context.message_context import message_context_manager, MessageState
from .services.context_grabber_service import ContextGrabberService
from .services.prompt_builder_service import PromptBuilderService
from .services.validation_service import ValidationService
from app.core.events.events import (
    UserMessageReceived, UserIdentified, ContextBuilt,
    LLMRequestReady, GenericResponse, ErrorOccurred
)

logger = logging.getLogger(__name__)


class PreprocessingActor(Actor):
    """Actor responsible for message preprocessing and context building."""
    
    def __init__(self, name: Optional[str] = None):
        super().__init__(name or "PreprocessingActor")
        self.context_grabber = None
        self.prompt_builder = None
        self.validation_service = None
    
    async def initialize(self) -> None:
        """Initialize the preprocessing actor."""
        try:
            logger.info(f"Initializing {self.name}")

            # Initialize services
            self.context_grabber = ContextGrabberService()
            self.prompt_builder = PromptBuilderService()
            self.validation_service = ValidationService()

            logger.info(f"{self.name} initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize {self.name}: {e}")
            raise
    
    async def start_actor(self) -> None:
        """Start the preprocessing actor."""
        logger.info(f"{self.name} started and ready to process messages")
    
    async def stop_actor(self) -> None:
        """Stop the preprocessing actor."""
        logger.info(f"{self.name} stopped successfully")
    
    @event_bus.subscribe("user_message_received")
    async def handle_user_message(self, event_data: UserMessageReceived) -> None:
        """Handle incoming user messages with context tracking."""
        try:
            logger.debug(f"Processing message from user {event_data.telegram_user_id} with context_id {event_data.context_id}")

            # Get the existing message context
            context = message_context_manager.get_context(event_data.context_id)
            if not context:
                logger.error(f"Context not found for context_id {event_data.context_id}")
                return

            # Update state to user identifying
            context.update_state(MessageState.USER_IDENTIFYING, self.name)

            # Request user identification from DBActor
            await event_bus.publish("user_identification_request", {
                "context_id": event_data.context_id,
                "context_id": context.context_id,
                "telegram_user_id": event_data.telegram_user_id,
                "chat_id": event_data.chat_id,
                "telegram_message_id": event_data.telegram_message_id,
                "user_info": {
                    "username": event_data.username,
                    "first_name": event_data.first_name,
                    "last_name": event_data.last_name
                },
                "timestamp": event_data.timestamp
            })

            # Store the message for later processing
            await event_bus.publish("store_message_request", {
                "context_id": context.context_id,
                "telegram_message_id": event_data.telegram_message_id,
                "chat_id": event_data.chat_id,
                "telegram_user_id": event_data.telegram_user_id,
                "message_text": event_data.message_text,
                "message_type": event_data.message_type,
                "is_from_bot": False,
                "telegram_timestamp": event_data.telegram_timestamp,
                "is_reply": event_data.is_reply,
                "reply_to_message_id": event_data.reply_to_message_id,
                "chat_type": event_data.chat_type,
                "chat_title": event_data.chat_title
            })

        except Exception as e:
            logger.error(f"Error handling user message: {e}")
            await self._publish_error("handle_user_message", str(e), event_data.__dict__)
    
    @event_bus.subscribe("user_identified")
    async def handle_user_identified(self, event_data: UserIdentified) -> None:
        """Handle user identification results and proceed to prevalidation."""
        try:
            context_id = event_data.__dict__.get("context_id")
            if not context_id:
                logger.error("No context_id in user_identified event")
                return

            context = message_context_manager.get_context(context_id)
            if not context:
                logger.error(f"Context {context_id} not found")
                return

            # Update context with user data
            context.user_data = event_data.user_data
            context.update_state(MessageState.USER_IDENTIFIED, self.name, {"user_exists": event_data.user_exists})

            if event_data.needs_registration:
                # New user needs registration
                logger.info(f"New user {event_data.telegram_user_id} needs registration")

                context.update_state(MessageState.COMPLETED, self.name, {"reason": "new_user_registration"})

                # Send registration prompt
                await event_bus.publish("generic_response", GenericResponse(
                    chat_id=event_data.chat_id,
                    message_type="new_user",
                    reply_to_message_id=event_data.telegram_message_id
                ))

                return

            # Existing user, proceed to prevalidation
            logger.debug(f"User {event_data.telegram_user_id} identified, proceeding to prevalidation")
            await self._prevalidate_message(context)

        except Exception as e:
            logger.error(f"Error handling user identification: {e}")
            await self._publish_error("handle_user_identified", str(e), event_data.__dict__)

    async def _prevalidate_message(self, context) -> None:
        """Prevalidate message to determine if LLM processing is needed."""
        try:
            context.update_state(MessageState.VALIDATING, self.name)

            # Validate the message
            validation_result = await self.validation_service.validate_and_classify_message(context.message_text)
            context.validation_result = validation_result

            if not validation_result.get("needs_llm", True):
                # Message doesn't need LLM processing, send suggested response
                context.update_state(MessageState.COMPLETED, self.name, {
                    "reason": "prevalidation_filtered",
                    "classification": validation_result.get("classification"),
                    "confidence": validation_result.get("confidence")
                })

                suggested_response = validation_result.get("suggested_response")
                if suggested_response:
                    await event_bus.publish("send_message_request", {
                        "chat_id": context.chat_id,
                        "message_text": suggested_response,
                        "reply_to_message_id": context.telegram_message_id
                    })
                return

            # Message passed validation, proceed to context building
            context.update_state(MessageState.VALIDATED, self.name, {
                "classification": validation_result.get("classification"),
                "confidence": validation_result.get("confidence"),
                "relevance_score": validation_result.get("relevance_score")
            })

            await self._build_context_and_prompt(context)

        except Exception as e:
            logger.error(f"Error in prevalidation: {e}")
            context.add_error(f"Prevalidation failed: {str(e)}", self.name)
            await self._publish_error("prevalidate_message", str(e), {"context_id": context.context_id})
    

    
    async def _build_context_and_prompt(self, context) -> None:
        """Build context and prompt for LLM processing."""
        try:
            context.update_state(MessageState.CONTEXT_BUILDING, self.name)

            # Get comprehensive context
            context_data = await self.context_grabber.get_user_context(
                context.telegram_user_id, context.chat_id, context.message_text
            )
            context.context_data = context_data

            # Get conversation history
            conversation_history = context_data.get("conversation_history", [])
            context.conversation_history = conversation_history

            # Build the prompt
            prompt = await self.prompt_builder.build_prompt(
                message_text=context.message_text,
                context=context_data,
                conversation_history=conversation_history,
                user_data=context.user_data
            )
            context.prompt = prompt

            context.update_state(MessageState.CONTEXT_BUILT, self.name, {
                "context_size": len(str(context_data)),
                "history_messages": len(conversation_history),
                "prompt_length": len(prompt)
            })

            # Publish LLM request ready event
            await event_bus.publish("llm_request_ready", LLMRequestReady(
                telegram_user_id=context.telegram_user_id,
                chat_id=context.chat_id,
                telegram_message_id=context.telegram_message_id,
                prompt=prompt,
                context=context_data
            ))

        except Exception as e:
            logger.error(f"Error building context and prompt: {e}")
            context.add_error(f"Context building failed: {str(e)}", self.name)
            await self._publish_error("build_context_and_prompt", str(e), {"context_id": context.context_id})
    
    async def _publish_error(self, operation: str, error_message: str, original_event: Dict[str, Any]) -> None:
        """Publish an error event."""
        await event_bus.publish("error_occurred", ErrorOccurred(
            user_id=original_event.get("telegram_user_id"),
            chat_id=original_event.get("chat_id"),
            message_id=original_event.get("telegram_message_id"),
            error_type="preprocessing_error",
            error_message=error_message,
            component=f"{self.name}.{operation}"
        ))
