"""
Database infrastructure components.

This module contains the database layer including models, repositories,
services, and the database actor for MongoDB operations.
"""

from .connection_manager import ConnectionManager, get_connection_manager
from .interfaces import IDatabaseService, IUserRepository, IMessageRepository, IDatabaseConnection
from .repositories.bot_user_repository import BotUserRepository
from .repositories.conversation_message_repository import ConversationMessageRepository
from .services.database_service import DatabaseService, database_service
from .db_actor import DBActor

__all__ = [
    "ConnectionManager",
    "get_connection_manager",
    "IDatabaseService",
    "IUserRepository",
    "IMessageRepository",
    "IDatabaseConnection",
    "BotUserRepository",
    "ConversationMessageRepository",
    "DatabaseService",
    "database_service",
    "DBActor"
]