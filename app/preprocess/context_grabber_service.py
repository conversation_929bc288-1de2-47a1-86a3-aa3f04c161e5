"""
Context Grabber Service for retrieving user context and conversation history.

This service is responsible for gathering relevant context for user messages,
including conversation history, user preferences, and relevant past interactions.
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional

from app.common.models import BotUser, ConversationMessage
from app.db import database_service

logger = logging.getLogger(__name__)


class ContextGrabberService:
    """Service for grabbing user context and conversation history."""
    
    def __init__(self):
        self.db_service = database_service
    
    async def get_user_context(
        self, 
        telegram_user_id: int, 
        chat_id: int,
        message_text: str
    ) -> Dict[str, Any]:
        """
        Get comprehensive user context for message processing.
        
        Args:
            telegram_user_id: Telegram user ID
            chat_id: Chat ID where message was sent
            message_text: The current message text
            
        Returns:
            Dictionary containing user context information
        """
        try:
            # Get user information
            user = await self._get_user_info(telegram_user_id)
            
            # Get conversation history
            conversation_history = await self._get_conversation_history(
                chat_id, telegram_user_id
            )
            
            # Get recent context (last few hours)
            recent_context = await self._get_recent_context(chat_id)
            
            # Analyze message patterns
            message_patterns = await self._analyze_message_patterns(
                telegram_user_id, message_text
            )
            
            context = {
                "user": user,
                "conversation_history": conversation_history,
                "recent_context": recent_context,
                "message_patterns": message_patterns,
                "current_message": {
                    "text": message_text,
                    "timestamp": datetime.now(),
                    "chat_id": chat_id
                }
            }
            
            logger.debug(f"Built context for user {telegram_user_id} with {len(conversation_history)} history messages")
            return context
            
        except Exception as e:
            logger.error(f"Error getting user context: {e}")
            return {
                "user": None,
                "conversation_history": [],
                "recent_context": [],
                "message_patterns": {},
                "current_message": {
                    "text": message_text,
                    "timestamp": datetime.now(),
                    "chat_id": chat_id
                },
                "error": str(e)
            }
    
    async def _get_user_info(self, telegram_user_id: int) -> Optional[Dict[str, Any]]:
        """Get user information from database."""
        try:
            user = await self.db_service.get_user_by_telegram_id(telegram_user_id)
            if user:
                return {
                    "telegram_user_id": user.telegram_user_id,
                    "username": user.username,
                    "first_name": user.first_name,
                    "last_name": user.last_name,
                    "timezone": user.timezone,
                    "is_active": user.is_active,
                    "total_messages": user.total_messages,
                    "created_at": user.created_at,
                    "last_seen_at": user.last_seen_at
                }
            return None
        except Exception as e:
            logger.error(f"Error getting user info: {e}")
            return None
    
    async def _get_conversation_history(
        self, 
        chat_id: int, 
        telegram_user_id: int,
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """Get recent conversation history."""
        try:
            messages = await self.db_service.get_conversation_history(
                chat_id, telegram_user_id, limit=limit
            )
            
            history = []
            for message in messages:
                history.append({
                    "message_text": message.message_text,
                    "is_from_bot": message.is_from_bot,
                    "timestamp": message.telegram_timestamp,
                    "message_type": message.message_type,
                    "processing_status": message.processing_status
                })
            
            return history
            
        except Exception as e:
            logger.error(f"Error getting conversation history: {e}")
            return []
    
    async def _get_recent_context(
        self, 
        chat_id: int, 
        hours: int = 24
    ) -> List[Dict[str, Any]]:
        """Get recent messages in the chat for context."""
        try:
            messages = await self.db_service.get_recent_messages(
                chat_id, hours=hours, limit=20
            )
            
            context = []
            for message in messages:
                context.append({
                    "message_text": message.message_text,
                    "is_from_bot": message.is_from_bot,
                    "timestamp": message.telegram_timestamp,
                    "telegram_user_id": message.telegram_user_id
                })
            
            return context
            
        except Exception as e:
            logger.error(f"Error getting recent context: {e}")
            return []
    
    async def _analyze_message_patterns(
        self, 
        telegram_user_id: int, 
        current_message: str
    ) -> Dict[str, Any]:
        """Analyze user's message patterns for better context."""
        try:
            # Get user's recent messages for pattern analysis
            user_messages = await self.db_service.get_user_recent_messages(
                telegram_user_id, limit=50
            )
            
            patterns = {
                "message_length_avg": 0,
                "common_topics": [],
                "time_patterns": {},
                "message_frequency": 0,
                "preferred_response_style": "casual"
            }
            
            if user_messages:
                # Calculate average message length
                total_length = sum(len(msg.message_text) for msg in user_messages)
                patterns["message_length_avg"] = total_length / len(user_messages)
                
                # Analyze time patterns (simplified)
                time_hours = [msg.telegram_timestamp.hour for msg in user_messages]
                patterns["time_patterns"] = {
                    "most_active_hour": max(set(time_hours), key=time_hours.count) if time_hours else 12,
                    "activity_spread": len(set(time_hours))
                }
                
                # Message frequency (messages per day)
                if len(user_messages) > 1:
                    time_span = (user_messages[0].telegram_timestamp - user_messages[-1].telegram_timestamp).days
                    patterns["message_frequency"] = len(user_messages) / max(time_span, 1)
            
            return patterns
            
        except Exception as e:
            logger.error(f"Error analyzing message patterns: {e}")
            return {
                "message_length_avg": 0,
                "common_topics": [],
                "time_patterns": {},
                "message_frequency": 0,
                "preferred_response_style": "casual"
            }
