# Zeitwahl AI Agent 🗓️

An intelligent, event-driven calendar assistant built with Python that helps users manage their time, schedule meetings, and organize calendar events through natural language conversations via Telegram.

## Features

- **Event-Driven Architecture**: Loosely coupled components communicating through an optimized event bus
- **Multi-Provider LLM Support**: Gemini, Deepseek, and mock providers with automatic failover
- **Calendar Integration**: Support for Google Calendar, Outlook, and mock providers
- **Natural Language Processing**: Intelligent message preprocessing and context building
- **Smart Scheduling**: Conflict detection, available time slot finding, and working hours respect
- **User Management**: Session tracking, analytics, and preference management
- **Robust Error Handling**: Comprehensive validation, rate limiting, and graceful error recovery
- **Comprehensive Testing**: pytest-based unit tests with realistic failure scenarios

## Architecture

The system follows KISS principles with clear separation of concerns:

```
app/
├── core/                    # Core system components
│   ├── actors/             # Actor system and base actor
│   ├── events/             # Event bus and event definitions
│   └── context/            # Message context management
├── infrastructure/          # Infrastructure and external integrations
│   ├── database/           # Database layer (MongoDB)
│   │   ├── models/         # Database entity models
│   │   ├── repositories/   # Data access repositories
│   │   └── services/       # Database business logic
│   ├── telegram/           # Telegram bot integration
│   │   ├── handlers/       # Message and command handlers
│   │   └── services/       # Bot-related services
│   └── llm/               # LLM API integrations
│       ├── providers/      # Different LLM providers
│       └── services/       # LLM business logic
├── domain/                 # Business domain logic
│   ├── preprocessing/      # Message preprocessing pipeline
│   │   └── services/       # Preprocessing services
│   ├── postprocessing/     # Response postprocessing pipeline
│   │   └── services/       # Postprocessing services
│   └── scheduling/         # Calendar and scheduling logic
│       └── services/       # Scheduling services
├── config/                 # Configuration management
│   ├── settings/           # Environment settings
│   └── prompts/            # System prompts
├── utils/                  # Shared utilities and helpers
│   ├── logging/            # Custom logging utilities
│   └── constants/          # Application constants
└── main.py                 # Application entry point
```

### Event Flow

1. **Message Reception**: Telegram bot receives user message
2. **Preprocessing**: Message validation → User identification → Context building → Prompt construction
3. **LLM Processing**: Multi-provider LLM generates response with tool calls
4. **Postprocessing**: Response validation → Tool execution → Final response assembly
5. **Response Delivery**: Formatted response sent back to user

## Quick Start

### Prerequisites

- Python 3.13+
- Telegram Bot Token (from @BotFather)
- Optional: LLM API keys (Gemini, Deepseek)
- Optional: Calendar API credentials (Google, Outlook)

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd zeitwahl
```

2. Install dependencies:
```bash
pip install -e .
```

3. Set environment variables:
```bash
export TELEGRAM_BOT_TOKEN="your-telegram-bot-token"
export GEMINI_API_KEY="your-gemini-api-key"  # Optional
export DEEPSEEK_API_KEY="your-deepseek-api-key"  # Optional
```

### Running the Application

#### Development Mode
```bash
# Run the example demonstration (no real bot token needed)
python example_usage.py

# Run the full application
python -m app.main
```

#### Production Mode
```bash
export ENVIRONMENT=production
export LOG_LEVEL=INFO
python -m app.main
```

### Testing

Run the comprehensive test suite:
```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=app

# Run specific test file
pytest tests/test_event_bus.py -v
```

## Configuration

The system uses environment-based configuration with sensible defaults:

### Required Settings
- `TELEGRAM_BOT_TOKEN`: Your Telegram bot token

### Optional Settings
- `LLM_PRIMARY_PROVIDER`: Primary LLM provider (default: "gemini")
- `GEMINI_API_KEY`: Google Gemini API key
- `DEEPSEEK_API_KEY`: Deepseek API key
- `GOOGLE_CLIENT_ID`: Google Calendar integration
- `OUTLOOK_CLIENT_ID`: Outlook Calendar integration
- `LOG_LEVEL`: Logging level (default: "INFO")
- `ENVIRONMENT`: Environment (development/staging/production)

## Usage Examples

### Basic Calendar Operations

```python
# The bot understands natural language:
"Schedule a meeting tomorrow at 2 PM"
"What's on my calendar today?"
"Find me a free hour this week"
"Cancel my 3 PM meeting"
```

### Advanced Features

```python
# Multi-participant scheduling
"Schedule a team <NAME_EMAIL> and <EMAIL> for next Tuesday"

# Recurring events
"Set up a weekly standup every Monday at 9 AM"

# Conflict resolution
"Move my 2 PM meeting to avoid the conflict"
```

## Development

### Adding New LLM Providers

1. Create a new provider class inheriting from `LLMProvider`
2. Implement required methods: `generate_response()`, `stream_response()`
3. Register in `LLMService._initialize_providers()`

### Adding New Calendar Providers

1. Create a new provider class inheriting from `CalendarProvider`
2. Implement CRUD operations for calendar events
3. Register in `CalendarService._initialize_providers()`

### Event-Driven Development

```python
from app.utils.event_bus import event_bus
from app.utils.events import MessageReceived

class MyHandler:
    @event_bus.subscribe(MessageReceived)
    async def handle_message(self, event: MessageReceived):
        print(f"Received: {event.message_text}")

# Register handler
handler = MyHandler()
await event_bus.subscribe_tagged_methods(handler)
```

## Contributing

1. Follow KISS principles - keep it simple and minimal
2. Write tests for new features (pytest)
3. Use the event bus for component communication
4. Add proper error handling and logging
5. Update documentation for new features

## License

[Add your license here]

## Support

For questions and support, please [add contact information or issue tracker link].