"""
Database interfaces for loose coupling and modularity.

This module defines interfaces that repositories and services can implement
to ensure loose coupling and easy testing/mocking.
"""

from abc import ABC, abstractmethod
from typing import Optional, List, Dict, Any
from datetime import datetime

from app.common.models import BotUser, ConversationMessage


class IUserRepository(ABC):
    """Interface for user repository operations."""
    
    @abstractmethod
    async def create_user(self, user: <PERSON><PERSON><PERSON><PERSON>) -> BotU<PERSON>:
        """Create a new user."""
        pass
    
    @abstractmethod
    async def get_user_by_telegram_id(self, telegram_user_id: int) -> Optional[BotUser]:
        """Get a user by their Telegram user ID."""
        pass
    
    @abstractmethod
    async def update_user(self, user: <PERSON><PERSON><PERSON><PERSON>) -> BotUser:
        """Update an existing user."""
        pass
    
    @abstractmethod
    async def delete_user(self, telegram_user_id: int) -> bool:
        """Delete a user by their Telegram user ID."""
        pass
    
    @abstractmethod
    async def update_last_seen(self, telegram_user_id: int) -> bool:
        """Update a user's last seen timestamp."""
        pass
    
    @abstractmethod
    async def increment_message_count(self, telegram_user_id: int) -> bool:
        """Increment a user's message count."""
        pass


class IMessageRepository(ABC):
    """Interface for message repository operations."""
    
    @abstractmethod
    async def create_message(self, message: ConversationMessage) -> ConversationMessage:
        """Create a new message."""
        pass
    
    @abstractmethod
    async def get_message_by_telegram_id(
        self, 
        telegram_message_id: int, 
        chat_id: int
    ) -> Optional[ConversationMessage]:
        """Get a message by its Telegram message ID and chat ID."""
        pass
    
    @abstractmethod
    async def update_message(self, message: ConversationMessage) -> ConversationMessage:
        """Update an existing message."""
        pass
    
    @abstractmethod
    async def get_conversation_history(
        self,
        chat_id: int,
        telegram_user_id: int,
        limit: int = 10,
        offset: int = 0
    ) -> List[ConversationMessage]:
        """Get conversation history for a user in a chat."""
        pass
    
    @abstractmethod
    async def get_recent_messages(
        self,
        chat_id: int,
        hours: int = 24,
        limit: int = 100
    ) -> List[ConversationMessage]:
        """Get recent messages in a chat."""
        pass


class IDatabaseConnection(ABC):
    """Interface for database connection management."""
    
    @abstractmethod
    def connect(self) -> None:
        """Establish database connection."""
        pass
    
    @abstractmethod
    def disconnect(self) -> None:
        """Close database connection."""
        pass
    
    @abstractmethod
    def is_connected(self) -> bool:
        """Check if connected to database."""
        pass
    
    @abstractmethod
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check on the database connection."""
        pass


class IDatabaseService(ABC):
    """Interface for database service operations."""
    
    @abstractmethod
    async def initialize(self) -> None:
        """Initialize the database service."""
        pass
    
    @abstractmethod
    async def close(self) -> None:
        """Close database connections."""
        pass
    
    # User operations
    @abstractmethod
    async def get_user_by_telegram_id(self, telegram_user_id: int) -> Optional[BotUser]:
        """Get a user by their Telegram user ID."""
        pass
    
    @abstractmethod
    async def create_user(self, user: BotUser) -> BotUser:
        """Create a new user."""
        pass
    
    @abstractmethod
    async def update_user(self, user: BotUser) -> BotUser:
        """Update an existing user."""
        pass
    
    # Message operations
    @abstractmethod
    async def create_message(self, message: ConversationMessage) -> ConversationMessage:
        """Create a new message."""
        pass
    
    @abstractmethod
    async def get_message_by_telegram_id(
        self, 
        telegram_message_id: int, 
        chat_id: int
    ) -> Optional[ConversationMessage]:
        """Get a message by its Telegram message ID and chat ID."""
        pass
    
    @abstractmethod
    async def update_message(self, message: ConversationMessage) -> ConversationMessage:
        """Update an existing message."""
        pass
    
    @abstractmethod
    async def get_conversation_history(
        self,
        chat_id: int,
        telegram_user_id: int,
        limit: int = 10,
        offset: int = 0
    ) -> List[ConversationMessage]:
        """Get conversation history for a user in a chat."""
        pass
