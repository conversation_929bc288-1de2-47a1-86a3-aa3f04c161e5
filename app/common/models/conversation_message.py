"""
ConversationMessage entity model for MongoDB storage.

This module defines the ConversationMessage class that represents a message
in a conversation with essential information for linking users to conversations
and uniquely identifying messages.
"""

from datetime import datetime, timezone
from typing import Optional, Dict, Any
from pydantic import BaseModel, Field
from bson import ObjectId


class ConversationMessage(BaseModel):
    """
    ConversationMessage entity representing a message in a conversation.

    Simplified entity that contains essential information for message storage
    and retrieval, organized by arrival time within chat_ids.
    """

    # MongoDB document ID
    id: Optional[ObjectId] = Field(default=None, alias="_id")

    # Message identification
    telegram_message_id: int = Field(..., description="Unique Telegram message ID")
    chat_id: int = Field(..., description="Telegram chat ID where message was sent")

    # User identification (only for user messages, None for bot messages)
    telegram_user_id: Optional[int] = Field(default=None, description="Telegram user ID who sent the message (None for bot messages)")

    # Message content
    message_text: str = Field(..., description="The actual message content")
    message_type: str = Field(default="text", description="Type of message (text, photo, document, etc.)")

    # Message source
    is_from_bot: bool = Field(default=False, description="Whether this message is from the bot")

    # Timing information
    telegram_timestamp: datetime = Field(..., description="Original timestamp from Telegram")
    received_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc), description="When message was received by bot")

    # Conversation context
    is_reply: bool = Field(default=False, description="Whether this message is a reply")
    reply_to_message_id: Optional[int] = Field(default=None, description="ID of message being replied to")

    # Simple processing status
    processing_status: str = Field(default="received", description="Processing status (received, processing, processed, error)")

    # Chat context
    chat_type: str = Field(default="private", description="Type of chat (private, group, supergroup, channel)")
    chat_title: Optional[str] = Field(default=None, description="Title of the chat (for groups)")

    # Additional metadata
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional message metadata")
    
    class Config:
        """Pydantic configuration."""
        populate_by_name = True
        arbitrary_types_allowed = True
        json_encoders = {
            ObjectId: str,
            datetime: lambda v: v.isoformat()
        }

    def mark_as_processing(self) -> None:
        """Mark the message as being processed."""
        self.processing_status = "processing"

    def mark_as_processed(self) -> None:
        """Mark the message as processed."""
        self.processing_status = "processed"

    def mark_as_error(self) -> None:
        """Mark the message as having an error during processing."""
        self.processing_status = "error"

    def set_reply_context(self, reply_to_message_id: int) -> None:
        """Set reply context for this message."""
        self.is_reply = True
        self.reply_to_message_id = reply_to_message_id

    def update_chat_info(self, chat_type: str, chat_title: Optional[str] = None) -> None:
        """Update chat information."""
        self.chat_type = chat_type
        if chat_title:
            self.chat_title = chat_title

    def get_conversation_key(self) -> str:
        """Get a unique key for the conversation this message belongs to."""
        if self.telegram_user_id:
            return f"{self.chat_id}_{self.telegram_user_id}"
        else:
            return f"{self.chat_id}_bot"

    def to_dict(self) -> Dict[str, Any]:
        """Convert the model to a dictionary for MongoDB storage."""
        data = self.model_dump(by_alias=True, exclude_none=True)
        if self.id:
            data["_id"] = self.id
        return data

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "ConversationMessage":
        """Create a ConversationMessage instance from a dictionary."""
        if "_id" in data:
            data["id"] = data["_id"]
        return cls(**data)

    @classmethod
    def from_telegram_message(cls, telegram_message: Dict[str, Any], chat_id: int) -> "ConversationMessage":
        """Create a ConversationMessage from a Telegram message object."""
        user = telegram_message.get("from", {})

        return cls(
            telegram_message_id=telegram_message["message_id"],
            chat_id=chat_id,
            telegram_user_id=user.get("id"),
            message_text=telegram_message.get("text", ""),
            telegram_timestamp=datetime.fromtimestamp(telegram_message["date"]),
            chat_type=telegram_message.get("chat", {}).get("type", "private"),
            chat_title=telegram_message.get("chat", {}).get("title"),
            is_reply=bool(telegram_message.get("reply_to_message")),
            reply_to_message_id=telegram_message.get("reply_to_message", {}).get("message_id"),
            is_from_bot=user.get("is_bot", False)
        )

    @classmethod
    def create_bot_message(cls, chat_id: int, message_text: str,
                          reply_to_message_id: Optional[int] = None) -> "ConversationMessage":
        """Create a ConversationMessage for a bot message."""
        return cls(
            telegram_message_id=0,  # Bot messages don't have Telegram message IDs
            chat_id=chat_id,
            telegram_user_id=None,  # Bot messages don't have user IDs
            message_text=message_text,
            telegram_timestamp=datetime.now(timezone.utc),
            is_from_bot=True,
            is_reply=reply_to_message_id is not None,
            reply_to_message_id=reply_to_message_id
        )
